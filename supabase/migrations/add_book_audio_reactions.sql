-- Add book audio reactions support to existing reactions table
-- This extends the reactions table to support book audio posts and replies

-- First, add book_audio_post_id and book_audio_reply_id columns to reactions table
ALTER TABLE reactions ADD COLUMN book_audio_post_id UUID REFERENCES book_audio_posts(id) ON DELETE CASCADE;
ALTER TABLE reactions ADD COLUMN book_audio_reply_id UUID REFERENCES book_audio_replies(id) ON DELETE CASCADE;

-- Update the unique constraint to include book audio reactions
-- Drop the old constraint first
ALTER TABLE reactions DROP CONSTRAINT IF EXISTS reactions_diary_entry_id_user_id_key;
ALTER TABLE reactions DROP CONSTRAINT IF EXISTS reactions_book_id_user_id_key;

-- Add new comprehensive unique constraints
-- One reaction per user per diary entry
ALTER TABLE reactions ADD CONSTRAINT reactions_diary_entry_user_unique 
  UNIQUE(diary_entry_id, user_id) 
  WHERE diary_entry_id IS NOT NULL;

-- One reaction per user per book
ALTER TABLE reactions ADD CONSTRAINT reactions_book_user_unique 
  UNIQUE(book_id, user_id) 
  WHERE book_id IS NOT NULL;

-- One reaction per user per audio post
ALTER TABLE reactions ADD CONSTRAINT reactions_audio_post_user_unique 
  UNIQUE(audio_post_id, user_id) 
  WHERE audio_post_id IS NOT NULL;

-- One reaction per user per book audio post
ALTER TABLE reactions ADD CONSTRAINT reactions_book_audio_post_user_unique 
  UNIQUE(book_audio_post_id, user_id) 
  WHERE book_audio_post_id IS NOT NULL;

-- One reaction per user per book audio reply
ALTER TABLE reactions ADD CONSTRAINT reactions_book_audio_reply_user_unique 
  UNIQUE(book_audio_reply_id, user_id) 
  WHERE book_audio_reply_id IS NOT NULL;

-- Add check constraint to ensure only one content type is referenced
ALTER TABLE reactions ADD CONSTRAINT reactions_single_content_check 
  CHECK (
    (diary_entry_id IS NOT NULL)::int + 
    (book_id IS NOT NULL)::int + 
    (audio_post_id IS NOT NULL)::int + 
    (book_audio_post_id IS NOT NULL)::int + 
    (book_audio_reply_id IS NOT NULL)::int = 1
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reactions_book_audio_post_id ON reactions(book_audio_post_id);
CREATE INDEX IF NOT EXISTS idx_reactions_book_audio_reply_id ON reactions(book_audio_reply_id);
CREATE INDEX IF NOT EXISTS idx_reactions_book_audio_post_user ON reactions(book_audio_post_id, user_id);
CREATE INDEX IF NOT EXISTS idx_reactions_book_audio_reply_user ON reactions(book_audio_reply_id, user_id);

-- Add support for nested replies in book_audio_replies table
ALTER TABLE book_audio_replies ADD COLUMN parent_reply_id UUID REFERENCES book_audio_replies(id) ON DELETE CASCADE;
CREATE INDEX IF NOT EXISTS idx_book_audio_replies_parent ON book_audio_replies(parent_reply_id);

-- Add reaction counts to book audio posts and replies for performance
-- These will be updated via triggers or application logic
ALTER TABLE book_audio_posts ADD COLUMN reaction_counts JSONB DEFAULT '{}';
ALTER TABLE book_audio_replies ADD COLUMN reaction_counts JSONB DEFAULT '{}';

-- Create function to update reaction counts
CREATE OR REPLACE FUNCTION update_book_audio_reaction_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Handle book audio post reactions
  IF NEW.book_audio_post_id IS NOT NULL THEN
    UPDATE book_audio_posts 
    SET reaction_counts = (
      SELECT COALESCE(jsonb_object_agg(reaction_type, count), '{}')
      FROM (
        SELECT reaction_type, COUNT(*)::int as count
        FROM reactions 
        WHERE book_audio_post_id = NEW.book_audio_post_id
        GROUP BY reaction_type
      ) counts
    )
    WHERE id = NEW.book_audio_post_id;
  END IF;

  -- Handle book audio reply reactions
  IF NEW.book_audio_reply_id IS NOT NULL THEN
    UPDATE book_audio_replies 
    SET reaction_counts = (
      SELECT COALESCE(jsonb_object_agg(reaction_type, count), '{}')
      FROM (
        SELECT reaction_type, COUNT(*)::int as count
        FROM reactions 
        WHERE book_audio_reply_id = NEW.book_audio_reply_id
        GROUP BY reaction_type
      ) counts
    )
    WHERE id = NEW.book_audio_reply_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create function to handle reaction deletions
CREATE OR REPLACE FUNCTION update_book_audio_reaction_counts_on_delete()
RETURNS TRIGGER AS $$
BEGIN
  -- Handle book audio post reactions
  IF OLD.book_audio_post_id IS NOT NULL THEN
    UPDATE book_audio_posts 
    SET reaction_counts = (
      SELECT COALESCE(jsonb_object_agg(reaction_type, count), '{}')
      FROM (
        SELECT reaction_type, COUNT(*)::int as count
        FROM reactions 
        WHERE book_audio_post_id = OLD.book_audio_post_id
        GROUP BY reaction_type
      ) counts
    )
    WHERE id = OLD.book_audio_post_id;
  END IF;

  -- Handle book audio reply reactions
  IF OLD.book_audio_reply_id IS NOT NULL THEN
    UPDATE book_audio_replies 
    SET reaction_counts = (
      SELECT COALESCE(jsonb_object_agg(reaction_type, count), '{}')
      FROM (
        SELECT reaction_type, COUNT(*)::int as count
        FROM reactions 
        WHERE book_audio_reply_id = OLD.book_audio_reply_id
        GROUP BY reaction_type
      ) counts
    )
    WHERE id = OLD.book_audio_reply_id;
  END IF;

  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic reaction count updates
CREATE TRIGGER book_audio_reaction_insert_trigger
  AFTER INSERT ON reactions
  FOR EACH ROW
  WHEN (NEW.book_audio_post_id IS NOT NULL OR NEW.book_audio_reply_id IS NOT NULL)
  EXECUTE FUNCTION update_book_audio_reaction_counts();

CREATE TRIGGER book_audio_reaction_delete_trigger
  AFTER DELETE ON reactions
  FOR EACH ROW
  WHEN (OLD.book_audio_post_id IS NOT NULL OR OLD.book_audio_reply_id IS NOT NULL)
  EXECUTE FUNCTION update_book_audio_reaction_counts_on_delete();
