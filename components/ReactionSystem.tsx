"use client"

import { useState, useEffect, useRef } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"

interface ReactionType {
  id: string
  emoji: string
  label: string
}

interface ReactionSystemProps {
  contentId: string
  contentType: 'diary' | 'audio' | 'book'
  currentUserId?: string
  initialReactions?: Record<string, number>
  userReaction?: string | null
  onReactionUpdate?: (reactions: Record<string, number>, userReaction: string | null) => void
}

const REACTION_TYPES: ReactionType[] = [
  { id: 'love', emoji: '❤️', label: 'Love' },
  { id: 'fire', emoji: '🔥', label: 'Fire' },
  { id: 'smile', emoji: '😊', label: 'Happy' },
  { id: 'cry', emoji: '😢', label: 'Sad' },
  { id: 'broken', emoji: '💔', label: 'Heartbroken' },
  { id: 'wow', emoji: '😮', label: 'Wow' },
  { id: 'laugh', emoji: '😂', label: 'Laugh' }
]

export function ReactionSystem({
  contentId,
  contentType,
  currentUserId,
  initialReactions = {},
  userReaction,
  onReactionUpdate
}: ReactionSystemProps) {
  const [reactions, setReactions] = useState<Record<string, number>>(initialReactions)
  const [currentUserReaction, setCurrentUserReaction] = useState<string | null>(userReaction || null)
  const [showPicker, setShowPicker] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const pickerRef = useRef<HTMLDivElement>(null)
  const supabase = createSupabaseClient()

  // Close picker when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {
        setShowPicker(false)
      }
    }

    if (showPicker) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showPicker])

  const totalReactions = Object.values(reactions).reduce((sum, count) => sum + count, 0)
  const userReactionEmoji = currentUserReaction ? REACTION_TYPES.find(r => r.id === currentUserReaction)?.emoji : null

  const handleReactionClick = async (reactionId: string) => {
    if (!currentUserId) {
      alert('Please log in to react')
      return
    }

    setIsLoading(true)
    setShowPicker(false)

    try {
      const wasCurrentReaction = currentUserReaction === reactionId
      let newReactions = { ...reactions }
      let newUserReaction: string | null = null

      // Remove old reaction if exists
      if (currentUserReaction) {
        await supabase
          .from('reactions')
          .delete()
          .eq(getContentColumn(), contentId)
          .eq('user_id', currentUserId)

        // Decrease old reaction count
        newReactions[currentUserReaction] = Math.max(0, (newReactions[currentUserReaction] || 0) - 1)
      }

      // Add new reaction if it's different from current
      if (!wasCurrentReaction) {
        const insertData: any = {
          user_id: currentUserId,
          reaction_type: reactionId
        }
        insertData[getContentColumn()] = contentId

        await supabase
          .from('reactions')
          .insert(insertData)

        // Increase new reaction count
        newReactions[reactionId] = (newReactions[reactionId] || 0) + 1
        newUserReaction = reactionId
      }

      setReactions(newReactions)
      setCurrentUserReaction(newUserReaction)
      onReactionUpdate?.(newReactions, newUserReaction)

    } catch (error) {
      console.error('Error updating reaction:', error)
      alert('Failed to update reaction')
    } finally {
      setIsLoading(false)
    }
  }

  const getContentColumn = () => {
    switch (contentType) {
      case 'diary': return 'diary_entry_id'
      case 'audio': return 'audio_post_id'
      case 'book': return 'book_id'
      default: return 'diary_entry_id'
    }
  }

  const handleHeartClick = () => {
    if (!currentUserId) {
      alert('Please log in to react')
      return
    }
    setShowPicker(!showPicker)
  }

  return (
    <div className="relative" ref={pickerRef}>
      {/* Main reaction button */}
      <button
        onClick={handleHeartClick}
        disabled={isLoading}
        className="group flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-all duration-200 disabled:opacity-50"
      >
        {/* Heart icon - hollow unless user has reacted */}
        <div className="relative">
          {currentUserReaction ? (
            <span className="text-lg">{userReactionEmoji}</span>
          ) : (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 000-6.364 4.5 4.5 0 00-6.364 0L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          )}
        </div>
        
        {/* Reaction count */}
        {totalReactions > 0 && (
          <span className="text-sm font-medium text-gray-700">
            {totalReactions}
          </span>
        )}
      </button>

      {/* Reaction picker popup */}
      {showPicker && (
        <div className="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-2 flex gap-1 z-50 min-w-max">
          {REACTION_TYPES.map((reaction) => (
            <button
              key={reaction.id}
              onClick={() => handleReactionClick(reaction.id)}
              disabled={isLoading}
              className={`flex flex-col items-center p-2 rounded-lg hover:bg-gray-100 transition-colors min-w-[50px] disabled:opacity-50 ${
                currentUserReaction === reaction.id
                  ? 'bg-blue-50 border border-blue-200'
                  : ''
              }`}
              title={reaction.label}
            >
              <span className="text-xl mb-1">{reaction.emoji}</span>
              <span className="text-xs text-gray-600 font-medium">
                {reactions[reaction.id] || 0}
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  )
}
